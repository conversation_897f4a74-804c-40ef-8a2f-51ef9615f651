'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getCurrentFlow } from '@/lib/flow-config';

/**
 * Flow-aware home page
 * Redirects to the appropriate flow-specific home page
 */
export default function Home() {
  const router = useRouter();

  useEffect(() => {
    const currentFlow = getCurrentFlow();

    // Redirect to flow-specific home page
    if (currentFlow === 'remix') {
      router.replace('/remix');
    } else if (currentFlow === 'artist') {
      router.replace('/discover');
    }
  }, [router]);

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}
