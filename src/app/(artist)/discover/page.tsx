import React from 'react';
import FlowGuard from '@/components/guards/flow-guard';

/**
 * Artist Flow - Discover Page
 * Main dashboard for the artist flow
 */
function ArtistDiscoverPageContent() {
  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Discover</h1>
          <p className="text-muted-foreground">
            Explore music, artists, and opportunities in the artist platform
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Featured Artists</h3>
            <p className="text-sm text-muted-foreground">
              Discover trending artists and their latest releases
            </p>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-2">New Releases</h3>
            <p className="text-sm text-muted-foreground">
              Check out the latest albums and singles
            </p>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Opportunities</h3>
            <p className="text-sm text-muted-foreground">
              Find collaboration and performance opportunities
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ArtistDiscoverPage() {
  return (
    <FlowGuard allowedFlows={['artist']}>
      <ArtistDiscoverPageContent />
    </FlowGuard>
  );
}
