'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { getCurrentFlow } from '@/lib/flow-config';

interface FlowGuardProps {
  children: React.ReactNode;
  allowedFlows: ('remix' | 'artist')[];
  redirectTo?: string;
}

/**
 * Flow Guard Component
 * Ensures that routes are only accessible in the correct flow
 */
export default function FlowGuard({ 
  children, 
  allowedFlows, 
  redirectTo 
}: FlowGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const currentFlow = getCurrentFlow();

  useEffect(() => {
    if (!allowedFlows.includes(currentFlow)) {
      // Determine redirect path
      let redirectPath = redirectTo;
      
      if (!redirectPath) {
        // Default redirect based on current flow
        redirectPath = currentFlow === 'remix' ? '/remix' : '/discover';
      }
      
      console.warn(
        `Route ${pathname} is not allowed in ${currentFlow} flow. Redirecting to ${redirectPath}`
      );
      
      router.replace(redirectPath);
      return;
    }
  }, [currentFlow, allowedFlows, pathname, redirectTo, router]);

  // Only render children if flow is allowed
  if (!allowedFlows.includes(currentFlow)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Redirecting...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
