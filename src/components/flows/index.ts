/**
 * Flow Components Index
 * Exports components based on the current flow configuration
 */

import { getCurrentFlow } from '../../lib/flow-config';

// Dynamic exports based on current flow
export function getFlowComponents() {
  const currentFlow = getCurrentFlow();
  
  switch (currentFlow) {
    case 'remix':
      return import('./remix');
    case 'artist':
      return import('./artist');
    default:
      throw new Error(`Unknown flow: ${currentFlow}`);
  }
}

// Static exports for direct imports
export * as RemixComponents from './remix';
export * as ArtistComponents from './artist';
