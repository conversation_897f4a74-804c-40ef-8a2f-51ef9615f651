'use client';

import React from 'react';
import { RemixThemeProvider } from '@/app/remix/remix-theme-provider';

interface RemixLayoutProps {
  children: React.ReactNode;
}

/**
 * Remix Flow Layout
 * - Custom theme provider with remix-specific styling
 * - No header or sidebar
 * - Full-screen layout for remix experience
 */
export default function RemixLayout({ children }: RemixLayoutProps) {
  return (
    <RemixThemeProvider>
      <div className="min-h-screen bg-background">
        {children}
      </div>
    </RemixThemeProvider>
  );
}
