'use client';

import React from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/shared/side-bar/app-sidebar';
import Header from '@/components/shared/header';
import MusicPlayer from '@/components/shared/music-player/music-player';
import MusicPlayerAwareLayout from '@/components/shared/music-player-aware-layout';

interface ArtistLayoutProps {
  children: React.ReactNode;
}

/**
 * Artist Flow Layout
 * - Standard layout with header and sidebar
 * - Music player integration
 * - Full navigation and discovery features
 */
export default function ArtistLayout({ children }: ArtistLayoutProps) {
  return (
    <SidebarProvider>
      <MusicPlayerAwareLayout>
        <AppSidebar />
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          <SidebarInset className="flex flex-col h-full overflow-hidden">
            <Header />
            <div className="flex-1 overflow-auto">
              {children}
            </div>
            <MusicPlayer />
          </SidebarInset>
        </div>
      </MusicPlayerAwareLayout>
    </SidebarProvider>
  );
}
