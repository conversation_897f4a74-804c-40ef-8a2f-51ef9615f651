'use client';

import React from 'react';
import { getCurrentFlowConfig } from '@/lib/flow-config';
import RemixLayout from './remix-layout';
import ArtistLayout from './artist-layout';

interface FlowLayoutProps {
  children: React.ReactNode;
}

/**
 * Flow Layout Component
 * Renders the appropriate layout based on the current flow configuration
 */
export default function FlowLayout({ children }: FlowLayoutProps) {
  const flowConfig = getCurrentFlowConfig();

  // Render flow-specific layout
  switch (flowConfig.flow) {
    case 'remix':
      return <RemixLayout>{children}</RemixLayout>;

    case 'artist':
      return <ArtistLayout>{children}</ArtistLayout>;

    default:
      console.warn(`Unknown flow: ${flowConfig.flow}, using fallback layout`);
      return (
        <div className="min-h-screen bg-background">
          {children}
        </div>
      );
  }
}
