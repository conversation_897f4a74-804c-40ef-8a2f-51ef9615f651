/**
 * Common Components Library
 * Shared components that can be used across different flows (remix, artist)
 */

// Re-export all common components for easy importing
export { default as AlbumDisplay } from '../shared/album-display';
export { default as MusicPlayer } from '../shared/music-player/music-player';
export { default as <PERSON>Button } from '../shared/music-player/play-button';
export { default as SongRow } from '../shared/music-player/song-row';
export { default as ThemeToggle } from '../shared/theme/theme-toggle';
export { default as LanguageToggle } from '../shared/language/language-toggle';

// Auth components (flow-agnostic)
export { default as LoginForm } from '../auth/login-form';
export { default as SignupForm } from '../auth/signup-form';
export { default as EmailVerificationForm } from '../auth/email-verification-form';

// File upload components (can be used in both flows)
export { FileUploadModal } from '../remix/file-upload-modal';
export { FileTypeIcons } from '../remix/file-type-icons';

// UI components (already flow-agnostic)
export * from '../ui';
