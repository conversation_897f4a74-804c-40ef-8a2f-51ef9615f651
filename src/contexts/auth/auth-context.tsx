"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import {
  getCurrentAuthUser,
  logoutUser,
  signupUser,
  verifyEmail,
  resendVerificationCode
} from "@/lib/auth-utils"
import { initCognitoAuth } from "@/lib/cognito"
import { getCurrentFlow } from "@/lib/flow-config"
import type {
  AuthUser,
  SignupResponse,
  VerificationResponse,
  AuthResponse
} from "@/types/auth"

interface AuthContextType {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
  signup: (email: string, password: string, username?: string) => Promise<SignupResponse>
  verifyUserEmail: (email: string, code: string) => Promise<VerificationResponse>
  resendCode: (email: string) => Promise<AuthResponse>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize Amplify and check for existing session
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const currentFlow = getCurrentFlow()
        console.log(`Initializing auth for ${currentFlow} flow`)

        // Initialize Cognito Auth with flow-specific configuration
        initCognitoAuth()

        // Small delay to ensure Amplify is fully configured
        await new Promise(resolve => setTimeout(resolve, 100))

        await checkAuthState()
      } catch (error) {
        console.error('Auth initialization error:', error)
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  async function checkAuthState() {
    try {
      setIsLoading(true)
      const currentUser = await getCurrentAuthUser()
      setUser(currentUser)
    } catch (error) {
      console.error('Auth state check error:', error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  async function signOut() {
    try {
      setIsLoading(true)
      await logoutUser()
      setUser(null)
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  async function refreshUser() {
    try {
      const currentUser = await getCurrentAuthUser()
      setUser(currentUser)
    } catch (error) {
      console.error("Refresh user error:", error)
      setUser(null)
    }
  }

  async function signup(email: string, password: string, username?: string): Promise<SignupResponse> {
    return await signupUser({ email, password, username })
  }

  async function verifyUserEmail(email: string, code: string): Promise<VerificationResponse> {
    const result = await verifyEmail({ email, code })
    if (result.success && result.user) {
      setUser(result.user)
    }
    return result
  }

  async function resendCode(email: string): Promise<AuthResponse> {
    return await resendVerificationCode(email)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    signOut,
    refreshUser,
    signup,
    verifyUserEmail,
    resendCode,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
