import { Amplify } from 'aws-amplify';
import type { ResourcesConfig } from 'aws-amplify';
import { getFlowCognitoConfig, getCurrentFlow } from './flow-config';

// Legacy configuration for backward compatibility
export const cognitoConfig = {
  region: process.env.NEXT_PUBLIC_COGNITO_REGION!,
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
  clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
};

// Get flow-specific Cognito configuration
export function getFlowSpecificCognitoConfig() {
  try {
    return getFlowCognitoConfig();
  } catch (error) {
    console.warn('Flow configuration not available, falling back to legacy config:', error);
    return cognitoConfig;
  }
}

// Initialize AWS Amplify with flow-specific Cognito configuration
export function initCognitoAuth() {
  const flowConfig = getFlowSpecificCognitoConfig();
  const currentFlow = getCurrentFlow();

  console.log(`Initializing Cognito for ${currentFlow} flow`);

  const amplifyConfig: ResourcesConfig = {
    Auth: {
      Cognito: {
        userPoolId: flowConfig.userPoolId,
        userPoolClientId: flowConfig.clientId,
        loginWith: {
          email: true,
          username: false
        }
        // Removed identityPoolId to avoid conflicts with API key auth
      }
    },
    API: {
      GraphQL: {
        endpoint: 'https://2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com/graphql',
        region: 'us-east-2',
        defaultAuthMode: 'apiKey' as const,
        apiKey: 'da2-hu56cxlaxreutk6qxxkxhab7ly'
      }
    }
  };

  // Configure region separately or through environment
  process.env.AWS_REGION = flowConfig.region;

  Amplify.configure(amplifyConfig);

  console.log(`Cognito initialized for ${currentFlow} flow with user pool: ${flowConfig.userPoolId}`);
}