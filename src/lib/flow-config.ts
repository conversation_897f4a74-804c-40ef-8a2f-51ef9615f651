/**
 * Flow Configuration System
 * Manages different application flows (remix vs artist) with environment-based control
 */

export type AppFlow = 'remix' | 'artist';

export interface FlowConfig {
  flow: AppFlow;
  name: string;
  description: string;
  routes: {
    home: string;
    onboarding: string;
    dashboard: string;
    auth: {
      login: string;
      signup: string;
      callback: string;
    };
  };
  cognito: {
    domain: string;
    region: string;
    userPoolId: string;
    clientId: string;
    redirectUri: string;
    identityPoolId?: string;
  };
  features: {
    hasHeader: boolean;
    hasSidebar: boolean;
    hasThemeProvider: boolean;
    customLayout: boolean;
  };
}

// Flow configurations
export const FLOW_CONFIGS: Record<AppFlow, FlowConfig> = {
  remix: {
    flow: 'remix',
    name: 'Remix Flow',
    description: 'Music remix and collaboration platform',
    routes: {
      home: '/remix',
      onboarding: '/remix/onboarding',
      dashboard: '/remix',
      auth: {
        login: '/login',
        signup: '/signup',
        callback: '/auth/callback',
      },
    },
    cognito: {
      domain: process.env.NEXT_PUBLIC_REMIX_COGNITO_DOMAIN || process.env.NEXT_PUBLIC_COGNITO_DOMAIN || '',
      region: process.env.NEXT_PUBLIC_REMIX_COGNITO_REGION || process.env.NEXT_PUBLIC_COGNITO_REGION || '',
      userPoolId: process.env.NEXT_PUBLIC_REMIX_COGNITO_USER_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || '',
      clientId: process.env.NEXT_PUBLIC_REMIX_COGNITO_CLIENT_ID || process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '',
      redirectUri: process.env.NEXT_PUBLIC_REMIX_COGNITO_REDIRECT_URI || process.env.NEXT_PUBLIC_COGNITO_REDIRECT_URI || '',
      identityPoolId: process.env.NEXT_PUBLIC_REMIX_COGNITO_IDENTITY_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
    },
    features: {
      hasHeader: false,
      hasSidebar: false,
      hasThemeProvider: true,
      customLayout: true,
    },
  },
  artist: {
    flow: 'artist',
    name: 'Artist Flow',
    description: 'Artist management and music platform',
    routes: {
      home: '/discover',
      onboarding: '/onboarding',
      dashboard: '/discover',
      auth: {
        login: '/login',
        signup: '/signup',
        callback: '/auth/callback',
      },
    },
    cognito: {
      domain: process.env.NEXT_PUBLIC_ARTIST_COGNITO_DOMAIN || process.env.NEXT_PUBLIC_COGNITO_DOMAIN || '',
      region: process.env.NEXT_PUBLIC_ARTIST_COGNITO_REGION || process.env.NEXT_PUBLIC_COGNITO_REGION || '',
      userPoolId: process.env.NEXT_PUBLIC_ARTIST_COGNITO_USER_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || '',
      clientId: process.env.NEXT_PUBLIC_ARTIST_COGNITO_CLIENT_ID || process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || '',
      redirectUri: process.env.NEXT_PUBLIC_ARTIST_COGNITO_REDIRECT_URI || process.env.NEXT_PUBLIC_COGNITO_REDIRECT_URI || '',
      identityPoolId: process.env.NEXT_PUBLIC_ARTIST_COGNITO_IDENTITY_POOL_ID || process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
    },
    features: {
      hasHeader: true,
      hasSidebar: true,
      hasThemeProvider: false,
      customLayout: false,
    },
  },
};

// Get current flow from environment
export function getCurrentFlow(): AppFlow {
  const envFlow = process.env.NEXT_PUBLIC_APP_FLOW as AppFlow;
  
  // Validate the flow value
  if (envFlow && (envFlow === 'remix' || envFlow === 'artist')) {
    return envFlow;
  }
  
  // Default to remix if not specified or invalid
  console.warn(`Invalid or missing NEXT_PUBLIC_APP_FLOW: ${envFlow}. Defaulting to 'remix'.`);
  return 'remix';
}

// Get current flow configuration
export function getCurrentFlowConfig(): FlowConfig {
  const currentFlow = getCurrentFlow();
  return FLOW_CONFIGS[currentFlow];
}

// Check if current flow is remix
export function isRemixFlow(): boolean {
  return getCurrentFlow() === 'remix';
}

// Check if current flow is artist
export function isArtistFlow(): boolean {
  return getCurrentFlow() === 'artist';
}

// Get flow-specific routes
export function getFlowRoutes() {
  return getCurrentFlowConfig().routes;
}

// Get flow-specific Cognito configuration
export function getFlowCognitoConfig() {
  return getCurrentFlowConfig().cognito;
}

// Get flow-specific features
export function getFlowFeatures() {
  return getCurrentFlowConfig().features;
}

// Validate flow configuration
export function validateFlowConfig(flow: AppFlow): boolean {
  const config = FLOW_CONFIGS[flow];
  
  if (!config) {
    console.error(`Flow configuration not found for: ${flow}`);
    return false;
  }
  
  // Check required Cognito configuration
  const { cognito } = config;
  const requiredFields = ['domain', 'region', 'userPoolId', 'clientId', 'redirectUri'];
  
  for (const field of requiredFields) {
    if (!cognito[field as keyof typeof cognito]) {
      console.error(`Missing required Cognito configuration for ${flow}: ${field}`);
      return false;
    }
  }
  
  return true;
}

// Initialize and validate current flow configuration
export function initializeFlowConfig(): FlowConfig {
  const currentFlow = getCurrentFlow();
  const isValid = validateFlowConfig(currentFlow);
  
  if (!isValid) {
    throw new Error(`Invalid flow configuration for: ${currentFlow}`);
  }
  
  const config = getCurrentFlowConfig();
  console.log(`Initialized ${config.name} (${config.flow})`);
  
  return config;
}
