# Flow-Based Architecture Refactoring

This document outlines the refactoring of the repository to support two separate flows (remix and artist) with environment-based control while sharing common components.

## Overview

The application now supports two distinct flows:
- **Remix Flow**: Music remix and collaboration platform
- **Artist Flow**: Artist management and music discovery platform

## Key Features

### 1. Environment-Based Flow Control
- Set `NEXT_PUBLIC_APP_FLOW=remix` or `NEXT_PUBLIC_APP_FLOW=artist` in `.env.local`
- Automatic flow detection and configuration
- Flow-specific routing and authentication

### 2. Separate Cognito Configurations
- Each flow can have its own Cognito user pool
- Environment variables for both flows:
  - `NEXT_PUBLIC_REMIX_COGNITO_*` for remix flow
  - `NEXT_PUBLIC_ARTIST_COGNITO_*` for artist flow
- Backward compatibility with legacy environment variables

### 3. Flow-Specific Layouts
- **Remix Flow**: Custom theme, no header/sidebar, full-screen experience
- **Artist Flow**: Standard layout with header, sidebar, and music player

### 4. Shared Component Library
- Common UI components in `src/components/common/`
- Flow-specific components in `src/components/flows/`
- Reusable authentication components

## File Structure

```
src/
├── lib/
│   ├── flow-config.ts          # Flow configuration and utilities
│   ├── cognito.ts              # Flow-aware Cognito setup
│   └── auth-routes.ts          # Flow-aware routing logic
├── components/
│   ├── common/                 # Shared components
│   ├── flows/
│   │   ├── remix/             # Remix-specific components
│   │   └── artist/            # Artist-specific components
│   ├── layouts/
│   │   ├── flow-layout.tsx    # Main flow router
│   │   ├── remix-layout.tsx   # Remix layout
│   │   └── artist-layout.tsx  # Artist layout
│   └── guards/
│       └── flow-guard.tsx     # Route protection by flow
├── app/
│   ├── (artist)/              # Artist flow routes
│   │   └── discover/
│   ├── remix/                 # Remix flow routes
│   ├── layout.tsx             # Updated root layout
│   └── page.tsx               # Flow-aware home page
└── contexts/
    └── auth/
        └── auth-context.tsx   # Flow-aware authentication
```

## Configuration

### Environment Variables

```bash
# Flow Configuration
NEXT_PUBLIC_APP_FLOW=remix  # or 'artist'

# Remix Flow Cognito
NEXT_PUBLIC_REMIX_COGNITO_DOMAIN=...
NEXT_PUBLIC_REMIX_COGNITO_USER_POOL_ID=...
NEXT_PUBLIC_REMIX_COGNITO_CLIENT_ID=...

# Artist Flow Cognito
NEXT_PUBLIC_ARTIST_COGNITO_DOMAIN=...
NEXT_PUBLIC_ARTIST_COGNITO_USER_POOL_ID=...
NEXT_PUBLIC_ARTIST_COGNITO_CLIENT_ID=...
```

### Flow Configuration

Each flow has its own configuration in `src/lib/flow-config.ts`:

```typescript
const FLOW_CONFIGS: Record<AppFlow, FlowConfig> = {
  remix: {
    routes: { home: '/remix', onboarding: '/remix/onboarding' },
    features: { hasHeader: false, hasSidebar: false },
    // ... cognito config
  },
  artist: {
    routes: { home: '/discover', onboarding: '/onboarding' },
    features: { hasHeader: true, hasSidebar: true },
    // ... cognito config
  }
};
```

## Usage

### Switching Flows

1. Update `NEXT_PUBLIC_APP_FLOW` in `.env.local`
2. Restart the development server
3. The application will automatically use the appropriate flow

### Adding Flow-Specific Routes

1. Create routes in the appropriate directory:
   - Remix: `src/app/remix/`
   - Artist: `src/app/(artist)/`

2. Wrap pages with FlowGuard:
```typescript
export default function MyPage() {
  return (
    <FlowGuard allowedFlows={['remix']}>
      <MyPageContent />
    </FlowGuard>
  );
}
```

### Using Shared Components

```typescript
// Import from common library
import { AlbumDisplay, MusicPlayer } from '@/components/common';

// Or flow-specific components
import { RemixComponents } from '@/components/flows';
```

## Benefits

1. **Single Codebase**: Both flows share the same repository
2. **Shared Components**: Common UI components reduce duplication
3. **Environment Control**: Easy switching between flows
4. **Separate Authentication**: Each flow can have its own user base
5. **Flow-Specific Features**: Each flow can have unique layouts and features
6. **Type Safety**: Full TypeScript support for flow configurations

## Migration Notes

- Existing routes continue to work with backward compatibility
- Legacy environment variables are still supported
- The default flow is 'remix' if not specified
- All existing components remain functional

## Next Steps

1. Configure separate Cognito user pools for each flow
2. Add flow-specific pages and features
3. Customize themes and styling per flow
4. Implement flow-specific business logic
5. Add flow-specific analytics and monitoring
